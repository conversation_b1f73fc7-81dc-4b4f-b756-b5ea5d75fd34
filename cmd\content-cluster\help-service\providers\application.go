package providers

import (
	"context"
	"fmt"
	"pxpat-backend/cmd"
	"pxpat-backend/internal/content-cluster/help-service/handler"
	"pxpat-backend/internal/content-cluster/help-service/routes"
	"pxpat-backend/internal/content-cluster/help-service/types"
	"pxpat-backend/pkg/consul"
	"pxpat-backend/pkg/middleware/cors"
	metricsMiddleware "pxpat-backend/pkg/middleware/metrics"
	tracingMiddleware "pxpat-backend/pkg/middleware/tracing"
	"pxpat-backend/pkg/opentelemetry"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
	"go.uber.org/fx"
)

// ProvideGinEngine 提供Gin引擎
func ProvideGinEngine(
	cfg *types.Config,
	otelProvider *opentelemetry.Provider,
	healthHandler *consul.HealthHandler,
	helpHandler *handler.HelpHandler,
) *gin.Engine {
	r := gin.Default()

	// 添加中间件
	r.Use(cors.CORSMiddleware(cfg.Security.Cors))

	// 添加指标收集中间件
	if otelProvider.IsMetricsEnabled() {
		r.Use(metricsMiddleware.Middleware(otelProvider.MetricsProvider(), cfg.Otlp.Metrics.ServiceName))
	}

	// 添加链路追踪中间件
	if otelProvider.IsTracingEnabled() {
		r.Use(tracingMiddleware.Middleware(otelProvider.TracingProvider(), cfg.Otlp.Tracing.ServiceName))
	}

	// 注册健康检查路由
	healthHandler.RegisterHealthRoutes(r)

	// 注册帮助中心路由
	routes.RegisterRoutes(r, helpHandler)

	log.Info().Msg("Gin引擎初始化成功")
	return r
}

// ManageLifecycle 应用生命周期管理
func ManageLifecycle(
	lc fx.Lifecycle,
	cfg *types.Config,
	otelProvider *opentelemetry.Provider,
	consulManager *consul.Manager,
	ginEngine *gin.Engine,
) {
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			// 启动Consul管理器
			if err := consulManager.Start(context.Background()); err != nil {
				fmt.Println("here")
				log.Fatal().Err(err).Msg("Consul管理器启动失败")
				return err
			}
			log.Info().Msg("Consul管理器启动成功")

			// 启动HTTP服务器（在goroutine中）
			go func() {
				log.Info().Msg("帮助中心服务启动中...")
				cmd.GraceStartAndClose(cfg.Server, ginEngine)
			}()

			return nil
		},
		OnStop: func(ctx context.Context) error {
			// 停止Consul管理器
			if err := consulManager.Stop(); err != nil {
				log.Error().Err(err).Msg("停止Consul管理器失败")
			} else {
				log.Info().Msg("Consul管理器停止成功")
			}

			// 关闭OpenTelemetry
			if err := otelProvider.Shutdown(ctx); err != nil {
				log.Error().Err(err).Msg("关闭 OpenTelemetry 失败")
			} else {
				log.Info().Msg("OpenTelemetry 关闭成功")
			}

			return nil
		},
	})

	log.Info().Msg("应用生命周期管理初始化成功")
}
