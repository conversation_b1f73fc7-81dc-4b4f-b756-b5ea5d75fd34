package main

import (
	"go.uber.org/fx"

	// 基础设施 providers
	"pxpat-backend/cmd/content-cluster/help-service/providers"

	// 业务层 providers
	"pxpat-backend/internal/content-cluster/help-service/handler"
	"pxpat-backend/internal/content-cluster/help-service/repository"
	"pxpat-backend/internal/content-cluster/help-service/service"
)



func main() {
	app := fx.New(
		// 基础设施层
		fx.Provide(
			providers.ProvideConfig,
			providers.ProvideOtelProvider,
			providers.ProvideDatabase,
			providers.ProvideConsulManager,
			providers.ProvideHealthHandler,
		),

		// 存储层
		fx.Provide(
			repository.ProvideHelpRepository, // 存储层
		),

		// 业务层
		fx.Provide(
			service.ProvideHelpService,
		),

		// 处理器层
		fx.Provide(
			handler.ProvideHelpHandler,
		),

		// 应用层
		fx.Provide(
			providers.ProvideGinEngine,
		),

		// 生命周期管理
		fx.Invoke(
			providers.ProvideLogger,
			providers.ManageLifecycle,
		),
	)

	// 运行应用
	app.Run()
}
